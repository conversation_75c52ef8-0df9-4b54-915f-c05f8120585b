#!/usr/bin/env python3
"""
基于yogo逻辑的Python实现
完整复制yogo的成功逻辑到Python
"""

import os
import re
import time
import uuid
import requests


from datetime import datetime
from typing import Dict, List, Optional, Any
from bs4 import BeautifulSoup
import logging

# 移除了User-Agent库的导入

class YogoPythonError(Exception):
    """自定义异常类"""
    pass

class CaptchaError(YogoPythonError):
    """验证码错误"""
    pass

class YogoPythonClient:
    """基于yogo逻辑的Python客户端"""

    def __init__(self, enable_debug: bool = False):
        self.ref_url = "https://yopmail.com"
        self.default_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36"
        self.default_timeout = 20

        self.enable_debug = enable_debug
        self.cookies: Dict[str, str] = {}
        self.api_version: Optional[str] = None

        # 设置日志
        self.logger = logging.getLogger(__name__)
        if enable_debug:
            self.logger.setLevel(logging.DEBUG)
        else:
            self.logger.setLevel(logging.WARNING)  # 只显示警告和错误

        # 初始化：获取API版本
        self._initialize()










    
    def _initialize(self):
        """初始化客户端，获取API版本"""
        try:
            response = self._fetch("GET", self.ref_url)
            self.api_version = self._parse_api_version(response.text)
            self.logger.info(f"获取到API版本: {self.api_version}")
        except Exception as e:
            raise YogoPythonError(f"初始化失败: {e}")
    
    def _create_http_client(self) -> requests.Session:
        """创建简单的HTTP客户端"""
        session = requests.Session()

        # 设置基本的User-Agent
        user_agent = os.getenv("YOGO_USER_AGENT", self.default_user_agent)
        session.headers.update({"User-Agent": user_agent})

        return session
    
    def _set_cookie(self, key: str, value: str):
        """设置cookie"""
        self.cookies[key] = value
    
    def _populate_cookie_from_account(self, account: str):
        """基于账户设置cookie（完全复制yogo逻辑）"""
        current_time = datetime.now().strftime("%H:%M")
        cookies_to_set = {
            "compte": account,
            "ywm": account, 
            "ytime": current_time
        }
        
        for key, value in cookies_to_set.items():
            self._set_cookie(key, value)
        
        self.logger.debug(f"设置账户cookie: {cookies_to_set}")
    
    def _build_cookie_string(self) -> str:
        """构建cookie字符串"""
        return "; ".join([f"{k}={v}" for k, v in self.cookies.items()])
    
    def _fetch(self, method: str, url: str, headers: Optional[Dict[str, str]] = None, data: Any = None) -> requests.Response:
        """发送HTTP请求（简化版）"""
        session = self._create_http_client()

        # 设置headers
        if headers:
            session.headers.update(headers)

        # 设置cookies
        if self.cookies:
            session.headers.update({"Cookie": self._build_cookie_string()})

        # 调试模式输出请求
        if self.enable_debug:
            self.logger.debug(f"请求: {method} {url}")

        try:
            response = session.request(method, url, data=data, timeout=self.default_timeout)

            # 调试模式输出响应
            if self.enable_debug:
                self.logger.debug(f"响应: {response.status_code} ({len(response.text)} 字符)")

            # 检查状态码
            if response.status_code >= 300:
                raise YogoPythonError(f"请求失败，状态码: {response.status_code}, 内容: {response.text[:200]}")

            # 更新cookies
            for cookie in response.cookies:
                if cookie.value:  # type: ignore
                    self.cookies[cookie.name] = cookie.value

            return response
            
        except requests.exceptions.Timeout as e:
            error_msg = f"请求超时: {e}"
            if self.proxy_session:
                error_msg += f" (使用代理session: {self.proxy_session})"
            print(f"⚠️  {error_msg}")
            raise YogoPythonError(error_msg)
        except requests.exceptions.ProxyError as e:
            error_msg = f"代理连接失败: {e}"
            if self.proxy_session:
                error_msg += f" (代理session: {self.proxy_session})"
            print(f"❌ {error_msg}")
            raise YogoPythonError(error_msg)
        except requests.exceptions.ConnectionError as e:
            error_msg = f"连接失败: {e}"
            if self.proxy_session:
                error_msg += f" (使用代理session: {self.proxy_session})"
            print(f"❌ {error_msg}")
            raise YogoPythonError(error_msg)
        except requests.exceptions.RequestException as e:
            error_msg = f"请求失败: {e}"
            if self.proxy_session:
                error_msg += f" (使用代理session: {self.proxy_session})"
            raise YogoPythonError(error_msg)
    
    def _parse_api_version(self, html_content: str) -> str:
        """解析API版本（复制yogo逻辑）"""
        pattern = r'<script src="/ver/([0-9.]+)/webmail\.js">'
        match = re.search(pattern, html_content)
        if not match:
            raise YogoPythonError("无法提取API版本")
        return match.group(1)
    
    def _decorate_url(self, base_url: str, disable_default_params: bool = False, query_params: Optional[Dict[str, str]] = None) -> str:
        """装饰URL，添加yp、yj等参数（完全复制yogo逻辑）"""
        # 1. 获取yp参数
        response = self._fetch("GET", self.ref_url)
        soup = BeautifulSoup(response.text, 'html.parser')
        yp_element = soup.find('input', {'id': 'yp'})
        if not yp_element or not hasattr(yp_element, 'get'):
            raise YogoPythonError("无法获取yp参数")
        yp_value = yp_element.get('value')  # type: ignore
        if not yp_value:
            raise YogoPythonError("无法获取yp参数")
        yp = str(yp_value)
        
        # 2. 获取yj参数
        webmail_url = f"{self.ref_url}/ver/{self.api_version}/webmail.js"
        webmail_response = self._fetch("GET", webmail_url)
        yj_pattern = r"&yj=(.*?)&"
        yj_match = re.search(yj_pattern, webmail_response.text)
        if not yj_match:
            raise YogoPythonError("无法获取yj参数")
        yj = yj_match.group(1)
        
        # 3. 构建URL
        from urllib.parse import urlencode, urlparse, parse_qs
        
        # 解析基础URL
        parsed_url = urlparse(f"{self.ref_url}/{base_url}")
        query_dict = parse_qs(parsed_url.query)
        
        # 添加默认参数
        if not disable_default_params:
            query_dict['yp'] = [yp]
            query_dict['yj'] = [yj]
            if self.api_version:
                query_dict['v'] = [self.api_version]

        # 添加自定义参数
        if query_params:
            for k, v in query_params.items():
                query_dict[k] = [v]
        
        # 构建最终URL
        # 将多值参数转换为单值
        final_params = {k: v[0] if isinstance(v, list) and v else v for k, v in query_dict.items()}
        query_string = urlencode(final_params)
        
        final_url = f"{self.ref_url}/en{parsed_url.path}?{query_string}"
        
        if self.enable_debug:
            self.logger.debug(f"装饰后的URL: {final_url}")
            self.logger.debug(f"yp: {yp}, yj: {yj}")
        
        return final_url
    
    def _check_inbox_captcha(self, content: str):
        """检查收件箱验证码（复制yogo逻辑）"""
        # yogo的正则表达式
        pattern = r"w\.finrmail\(\d+,\s*\d+,\s*\d+,\s*\d+,\s*\d+,\s*'alt\.[^']+',\s*'.*?'\)|Loading \.\.\."
        if not re.search(pattern, content):
            if self.enable_debug:
                self.logger.debug("检测到收件箱验证码")
                # 保存验证码响应内容用于分析
                try:
                    with open('captcha_response_848.html', 'w', encoding='utf-8') as f:
                        f.write(content)
                    self.logger.debug(f"验证码响应已保存到 captcha_response_848.html (长度: {len(content)})")
                except Exception as e:
                    self.logger.debug(f"保存验证码响应失败: {e}")
            raise CaptchaError("检测到收件箱验证码")
        else:
            if self.enable_debug:
                self.logger.debug("收件箱验证码检查通过")
    
    def _check_mail_captcha(self, content: str):
        """检查邮件验证码（优化版本，区分网络错误和真正验证码）"""
        # 检查是否为空内容或网络错误页面
        if not content or len(content.strip()) < 100:
            if self.enable_debug:
                self.logger.debug("邮件内容为空或过短，可能是网络错误")
            raise YogoPythonError("邮件内容获取失败，可能是网络问题")
        
        # 检查是否包含常见的错误页面标识
        error_indicators = [
            "503 Service Temporarily Unavailable",
            "502 Bad Gateway", 
            "504 Gateway Timeout",
            "Connection timed out",
            "Network Error",
            "Unable to connect"
        ]
        
        content_lower = content.lower()
        for indicator in error_indicators:
            if indicator.lower() in content_lower:
                if self.enable_debug:
                    self.logger.debug(f"检测到网络错误页面: {indicator}")
                raise YogoPythonError(f"网络错误: {indicator}")
        
        # 检查真正的验证码
        if "window.showRc()" in content:
            if self.enable_debug:
                self.logger.debug("检测到邮件验证码")
            raise CaptchaError("检测到邮件验证码")
        else:
            if self.enable_debug:
                self.logger.debug("邮件验证码检查通过")
    
    def get_mails_page(self, identifier: str, page: int = 1) -> BeautifulSoup:
        """获取邮件页面（复制yogo的GetMailsPage逻辑）"""
        # 设置账户cookie
        self._populate_cookie_from_account(identifier)
        
        # 构建URL（完全按照yogo的参数）
        base_url = "inbox"
        query_params = {
            "login": identifier,
            "p": str(page),
            "d": "",
            "ctrl": "",
            "scrl": "",
            "spam": "true",
            "ad": "0",
            "r_c": "",
            "id": ""
        }
        
        url = self._decorate_url(base_url, query_params=query_params)
        
        # 发送请求
        response = self._fetch("GET", url)
        
        # 检查验证码
        self._check_inbox_captcha(response.text)
        
        return BeautifulSoup(response.text, 'html.parser')
    
    def get_mail_page(self, identifier: str, mail_id: str, mail_type: str = "m") -> BeautifulSoup:
        """获取邮件内容页面（复制yogo的GetMailPage逻辑）"""
        # 设置账户cookie
        self._populate_cookie_from_account(identifier)
        
        # 构建URL
        base_url = "mail"
        query_params = {
            "b": identifier,
            "id": f"{mail_type}{mail_id}"
        }
        
        url = self._decorate_url(base_url, disable_default_params=True, query_params=query_params)
        
        # 发送请求
        response = self._fetch("GET", url)
        
        # 检查验证码
        self._check_mail_captcha(response.text)
        
        return BeautifulSoup(response.text, 'html.parser')

    # ==================== 域名管理功能（兼容旧yop模块接口） ====================

    def __init_domain_management(self):
        """初始化域名管理相关属性"""
        self._alternative_domains = None
        self._available_domains = None
        self.blacklist_file = os.path.join('input', 'yopmail_domain_blacklist.txt')
        # 确保input目录存在
        os.makedirs('input', exist_ok=True)

    def get_yopmail_alternative_domains(self) -> List[str]:
        """
        获取yopmail的所有备用域名（缓存机制）

        Returns:
            备用域名列表
        """
        # 初始化域名管理属性（如果还没有）
        if not hasattr(self, '_alternative_domains'):
            self.__init_domain_management()

        # 如果已缓存，直接返回
        if self._alternative_domains is not None:
            return self._alternative_domains

        if self.enable_debug:
            self.logger.debug("开始获取yopmail备用域名")

        try:
            # 访问yopmail域名列表页面获取备用域名（不使用代理）
            url = "https://yopmail.com/zh/domain?d=list"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            if self.enable_debug:
                self.logger.debug("访问yopmail域名列表页面获取备用域名")
            response = requests.get(url, headers=headers, timeout=15)

            if response.status_code == 200:
                content = response.text

                # 从域名列表页面中提取备用域名
                domains = self._extract_domains_from_response(content)

                if domains:
                    self._alternative_domains = domains
                    if self.enable_debug:
                        self.logger.debug(f"从域名列表页面获取到 {len(domains)} 个备用域名")
                    return domains

        except Exception as e:
            if self.enable_debug:
                self.logger.debug(f"从页面获取域名失败: {e}")

        # 如果获取失败，返回空列表，不使用内置域名
        if self.enable_debug:
            self.logger.debug("无法获取yopmail备用域名")
        self._alternative_domains = []
        return []

    def _extract_domains_from_response(self, content: str) -> List[str]:
        """
        从yopmail域名列表页面响应中提取域名

        Args:
            content: 页面HTML内容

        Returns:
            提取到的域名列表
        """
        import re

        # 使用正则表达式提取所有@域名格式
        # 页面中的格式是：<option>@domain.com</option>
        pattern = r'@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'

        matches = re.findall(pattern, content, re.IGNORECASE)

        if matches:
            # 去重并排序
            unique_domains = list(set(match.lower() for match in matches))
            unique_domains.sort()

            # 验证域名格式
            valid_domains = []
            for domain in unique_domains:
                if self._is_valid_domain(domain):
                    valid_domains.append(domain)

            return valid_domains
        else:
            if self.enable_debug:
                self.logger.debug("未找到任何@域名格式")
            return []

    def _is_valid_domain(self, domain: str) -> bool:
        """
        验证域名格式是否有效

        Args:
            domain: 域名

        Returns:
            是否有效
        """
        # 基本格式检查
        if not domain or len(domain) < 4:
            return False

        # 必须包含点
        if '.' not in domain:
            return False

        # 不能以点开头或结尾
        if domain.startswith('.') or domain.endswith('.'):
            return False

        # 不能包含连续的点
        if '..' in domain:
            return False

        return True

    def _load_domain_blacklist(self) -> List[str]:
        """
        加载域名黑名单

        Returns:
            黑名单域名列表
        """
        if not hasattr(self, 'blacklist_file'):
            self.__init_domain_management()

        blacklist = []

        try:
            if os.path.exists(self.blacklist_file):
                with open(self.blacklist_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        domain = line.strip()
                        if domain and not domain.startswith('#'):
                            blacklist.append(domain)
        except Exception as e:
            if self.enable_debug:
                self.logger.debug(f"加载黑名单文件失败: {e}")

        return blacklist

    def _save_domain_to_blacklist(self, domain: str):
        """
        将域名添加到黑名单

        Args:
            domain: 要添加的域名
        """
        if not hasattr(self, 'blacklist_file'):
            self.__init_domain_management()

        try:
            # 先检查是否已存在
            blacklist = self._load_domain_blacklist()
            if domain not in blacklist:
                # 简化的文件写入逻辑
                with open(self.blacklist_file, 'a', encoding='utf-8') as f:
                    f.write(f"{domain}\n")
                if self.enable_debug:
                    self.logger.debug(f"域名 {domain} 已添加到黑名单文件")
            else:
                if self.enable_debug:
                    self.logger.debug(f"域名 {domain} 已在黑名单中")
        except Exception as e:
            if self.enable_debug:
                self.logger.debug(f"添加域名到黑名单失败: {e}")

    def get_available_domains(self) -> List[str]:
        """
        获取可用的域名列表（排除黑名单）

        Returns:
            可用域名列表
        """
        # 初始化域名管理属性（如果还没有）
        if not hasattr(self, '_available_domains'):
            self.__init_domain_management()

        # 如果已缓存，直接返回
        if self._available_domains is not None:
            return self._available_domains

        # 获取所有备用域名
        all_domains = self.get_yopmail_alternative_domains()

        # 加载黑名单
        blacklist = self._load_domain_blacklist()

        # 过滤掉黑名单中的域名
        available_domains = [domain for domain in all_domains if domain not in blacklist]

        self._available_domains = available_domains
        if self.enable_debug:
            self.logger.debug(f"可用域名 {len(available_domains)} 个，黑名单 {len(blacklist)} 个")

        return available_domains

    def get_random_yopmail_domain(self) -> str:
        """
        随机获取一个可用的yopmail域名

        Returns:
            随机选择的域名
        """
        import random

        available_domains = self.get_available_domains()

        if not available_domains:
            if self.enable_debug:
                self.logger.debug("没有可用的yopmail域名，无法继续注册")
            raise Exception("没有可用的yopmail域名，请检查网络连接或yopmail网站状态")

        # 随机选择一个域名
        selected_domain = random.choice(available_domains)
        if self.enable_debug:
            self.logger.debug(f"随机选择域名: {selected_domain}")

        return selected_domain

    def mark_domain_as_blocked(self, domain: str, reason: str = "Resend blocked"):
        """
        标记域名为被阻止（添加到黑名单并从缓存中移除）

        Args:
            domain: 被阻止的域名
            reason: 阻止原因
        """
        if not hasattr(self, '_available_domains'):
            self.__init_domain_management()

        if self.enable_debug:
            self.logger.debug(f"域名 {domain} 被标记为阻止: {reason}")

        # 1. 添加到黑名单文件
        self._save_domain_to_blacklist(domain)

        # 2. 从可用域名缓存中移除
        if self._available_domains and domain in self._available_domains:
            self._available_domains.remove(domain)
            if self.enable_debug:
                self.logger.debug(f"已从可用域名缓存中移除: {domain}")

        # 3. 记录当前可用域名数量
        available_count = len(self._available_domains) if self._available_domains else 0
        if self.enable_debug:
            self.logger.debug(f"当前剩余可用域名: {available_count} 个")

    def get_domain_stats(self) -> Dict[str, Any]:
        """
        获取域名统计信息

        Returns:
            域名统计信息
        """
        all_domains = self.get_yopmail_alternative_domains()
        available_domains = self.get_available_domains()
        blacklist = self._load_domain_blacklist()

        return {
            "total_domains": len(all_domains),
            "available_domains": len(available_domains),
            "blacklisted_domains": len(blacklist),
            "all_domains_list": all_domains,
            "available_domains_list": available_domains,
            "blacklisted_domains_list": blacklist
        }


class MailItem:
    """邮件项目类"""
    def __init__(self, mail_id: str, sender_name: str = "", sender_email: str = "",
                 subject: str = "", is_spam: bool = False):
        self.id = mail_id
        self.sender_name = sender_name
        self.sender_email = sender_email
        self.subject = subject
        self.is_spam = is_spam

    def __repr__(self):
        return f"MailItem(id={self.id}, sender={self.sender_name or self.sender_email}, subject={self.subject[:30]}...)"


class YopmailInbox:
    """邮箱收件箱类"""

    def __init__(self, username: str, enable_debug: bool = False):
        self.username = username
        self.client = YogoPythonClient(enable_debug)
        self.mails: List[MailItem] = []

        # 域名缓存（兼容旧yop模块）
        self._alternative_domains = None
        self._available_domains = None

        # 黑名单文件路径（兼容旧yop模块）
        self.blacklist_file = os.path.join('input', 'yopmail_domain_blacklist.txt')

        # 确保input目录存在
        os.makedirs('input', exist_ok=True)

    def fetch_mails(self, limit: int = 10) -> List[MailItem]:
        """获取邮件列表（复制yogo的ParseInboxPages逻辑）"""
        self.mails = []
        items_per_page = 15  # yogo的itemNumber常量

        try:
            for page in range(1, (limit // items_per_page) + 2):
                if limit <= len(self.mails):
                    break

                # 获取页面
                soup = self.client.get_mails_page(self.username, page)

                # 解析邮件（复制yogo的parseInboxPage逻辑）
                self._parse_inbox_page(soup)

                # 延迟1秒（复制yogo逻辑）
                time.sleep(1)

            # 限制结果数量
            if len(self.mails) > limit:
                self.mails = self.mails[:limit]

            return self.mails

        except CaptchaError as e:
            # 验证码错误需要保持原类型，以便上层处理
            raise e
        except Exception as e:
            raise YogoPythonError(f"获取邮件失败: {e}")

    def _parse_inbox_page(self, soup: BeautifulSoup):
        """解析收件箱页面（完全复制yogo的parseInboxPage逻辑）"""
        mail_divs = soup.find_all('div', class_='m')

        for mail_div in mail_divs:
            try:
                # 获取邮件ID
                if not hasattr(mail_div, 'get'):
                    continue
                mail_id_attr = mail_div.get('id')  # type: ignore
                if not mail_id_attr:
                    continue
                mail_id = str(mail_id_attr)

                # 获取发件人信息
                if not hasattr(mail_div, 'find'):
                    continue
                sender_element = mail_div.find('span', {'class': 'lmf'})  # type: ignore
                sender_text = getattr(sender_element, 'text', '') if sender_element else ""

                # 检查是否为垃圾邮件
                is_spam = False
                if sender_text.startswith("[SPAM]"):
                    is_spam = True
                    sender_text = sender_text[6:]  # 移除[SPAM]前缀

                # 分离姓名和邮箱
                sender_name = ""
                sender_email = ""
                if "@" in sender_text:
                    sender_email = sender_text
                else:
                    sender_name = sender_text

                # 获取主题
                subject_element = mail_div.find('div', {'class': 'lms'})  # type: ignore
                subject = getattr(subject_element, 'text', '') if subject_element else ""

                # 创建邮件项目
                mail_item = MailItem(
                    mail_id=mail_id,
                    sender_name=sender_name,
                    sender_email=sender_email,
                    subject=subject,
                    is_spam=is_spam
                )

                self.mails.append(mail_item)

            except Exception as e:
                self.client.logger.warning(f"解析邮件失败: {e}")
                continue

    def get_mail_content(self, mail_id: str) -> str:
        """获取邮件内容"""
        try:
            soup = self.client.get_mail_page(self.username, mail_id)
            # 返回完整的HTML内容
            return str(soup)
        except Exception as e:
            raise YogoPythonError(f"获取邮件内容失败: {e}")

    def get_resend_verification_links(self, limit: int = 10) -> List[str]:
        """
        专门提取Resend发送的验证邮件中的认证链接

        Args:
            limit: 检查的邮件数量限制

        Returns:
            提取到的Resend认证链接列表
        """
        try:
            # 获取邮件列表
            mails = self.fetch_mails(limit)

            if not mails:
                if self.client.enable_debug:
                    self.client.logger.debug("收件箱中没有邮件")
                return []

            # 筛选Resend邮件
            resend_mails = []
            for mail in mails:
                if self._is_resend_mail(mail):
                    resend_mails.append(mail)

            if not resend_mails:
                if self.client.enable_debug:
                    self.client.logger.debug("未找到Resend发送的邮件")
                return []

            # 提取认证链接
            all_links = []
            for mail in resend_mails:
                try:
                    # 获取邮件内容
                    content = self.get_mail_content(mail.id)

                    # 提取认证链接
                    links = self._extract_resend_auth_links(content)

                    if links:
                        all_links.extend(links)

                except Exception as e:
                    if self.client.enable_debug:
                        self.client.logger.debug(f"处理邮件失败: {e}")
                    continue

            # 去重
            unique_links = list(set(all_links))

            return unique_links

        except CaptchaError as e:
            # 验证码错误需要传播出去，触发代理逻辑
            raise e
        except YogoPythonError as e:
            # 检查是否是验证码相关的错误
            if "验证码" in str(e) or "captcha" in str(e).lower():
                raise CaptchaError(str(e))
            else:
                return []
        except Exception as e:
            if self.client.enable_debug:
                self.client.logger.debug(f"提取Resend认证链接失败: {e}")
            return []

    def _is_resend_mail(self, mail: 'MailItem') -> bool:
        """判断是否为Resend发送的邮件"""
        # 检查发件人邮箱
        sender_indicators = [
            'resend.com',
            'notifications.resend.com',
            '<EMAIL>',
            '<EMAIL>'
        ]

        sender_email = mail.sender_email.lower() if mail.sender_email else ""
        sender_name = mail.sender_name.lower() if mail.sender_name else ""

        # 检查发件人邮箱是否包含resend相关域名
        for indicator in sender_indicators:
            if indicator in sender_email:
                return True

        # 检查发件人名称是否包含resend
        if 'resend' in sender_name:
            return True

        # 检查邮件主题是否包含resend相关关键词
        subject = mail.subject.lower() if mail.subject else ""
        subject_keywords = ['resend', 'confirm', 'verify', 'account']

        resend_in_subject = 'resend' in subject
        confirm_keywords = any(keyword in subject for keyword in subject_keywords[1:])

        return resend_in_subject and confirm_keywords

    def _extract_resend_auth_links(self, content: str) -> List[str]:
        """从邮件内容中提取Resend认证链接"""
        import re

        # Resend认证链接的特征模式
        patterns = [
            # 精确匹配resend认证链接
            r'https://resend\.com/auth/confirm-account[^"\s<>]*',
            # 备用模式：包含confirm-account的链接
            r'https?://[^"\s<>]*resend\.com[^"\s<>]*confirm-account[^"\s<>]*',
        ]

        found_links = []

        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)

            for match in matches:
                # 清理链接（移除HTML实体等）
                clean_link = match.replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
                clean_link = clean_link.replace('&quot;', '"').replace('&#39;', "'")

                # 确保链接以正确的格式开始
                if 'resend.com/auth/confirm-account' in clean_link and clean_link not in found_links:
                    found_links.append(clean_link)

        return found_links

# 主要函数：获取指定用户的Resend认证链接（带重试和代理逻辑）
def get_resend_verification_link(username: str, max_attempts: int = 5, wait_interval: int = 5) -> Optional[str]:
    """
    获取指定用户的Resend认证链接（简化版，去除代理逻辑）

    Args:
        username: 邮箱用户名（不含@yopmail.com）
        max_attempts: 最大尝试次数，默认5次
        wait_interval: 每次等待时间（秒），默认5秒

    Returns:
        最新的Resend认证链接，如果没有找到则返回None
    """
    # 获取main程序的logger
    main_logger = logging.getLogger('__main__')

    main_logger.info(f"📧 开始获取验证邮件: {username}@yopmail.com")

    for attempt in range(max_attempts):
        try:
            # 等待逻辑：第一次等3秒，后续等5秒
            if attempt == 0:
                wait_time = 3
                main_logger.info(f"⏳ 等待 {wait_time} 秒后开始第一次获取 (尝试 {attempt + 1}/{max_attempts})")
                time.sleep(wait_time)
            elif wait_interval > 0:
                main_logger.info(f"⏳ 等待 {wait_interval} 秒后重试获取 (尝试 {attempt + 1}/{max_attempts})")
                time.sleep(wait_interval)

            # 创建收件箱实例
            main_logger.info(f"🔗 获取邮件中...")
            inbox = YopmailInbox(username, enable_debug=False)

            # 获取Resend认证链接
            main_logger.info(f"📋 正在检查收件箱中的前10封邮件...")
            links = inbox.get_resend_verification_links(limit=10)

            if links:
                main_logger.info(f"✅ 成功找到验证链接！")
                main_logger.info(f"🔗 验证链接: {links[0][:50]}...")
                return links[0]  # 返回第一个链接
            else:
                main_logger.warning(f"❌ 未找到Resend验证邮件，继续重试...")
                continue

        except CaptchaError as e:
            main_logger.error(f"❌ 遇到验证码，需要使用CapSolver解决")
            continue

        except YogoPythonError as e:
            main_logger.warning(f"❌ 网络错误 (尝试 {attempt + 1}/{max_attempts}): {e}")
            continue

        except Exception as e:
            main_logger.error(f"❌ 获取邮件时出现异常 (尝试 {attempt + 1}/{max_attempts}): {e}")
            continue

    main_logger.error(f"❌ 尝试了 {max_attempts} 次都未找到验证邮件")
    return None



