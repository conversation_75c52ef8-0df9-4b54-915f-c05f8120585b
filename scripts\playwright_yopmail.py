"""
Playwright驱动的Yopmail邮件获取客户端
使用本地浏览器直连获取邮件，支持Capsolver验证码解决
"""

import asyncio
import logging
import os
import re
import time
from typing import Optional, List, Dict, Any
from playwright.async_api import async_playwright, <PERSON>rowser, Browser<PERSON>ontex<PERSON>, Page

# 尝试导入Capsolver
try:
    import capsolver
    HAS_CAPSOLVER = True
except ImportError:
    HAS_CAPSOLVER = False


class PlaywrightYopmailClient:
    """本地浏览器Yopmail邮件获取客户端"""
    
    def __init__(self, headless: bool = False):
        """
        初始化Yopmail客户端
        
        Args:
            headless: 是否使用无头模式，默认False（可视化模式便于调试）
        """
        # 浏览器相关
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.headless = headless
        
        # Capsolver配置
        self.capsolver_api_key = "CAP-CE2A3DE7A3F83A04839B991AE859957C663379F212F991F527EBDD88C9BBE3C2"
        self.capsolver_timeout = 120
        
        # 配置
        self.timeout = 30000  # 30秒超时
        self.logger = logging.getLogger(__name__)
        
        # 初始化Capsolver
        self._init_capsolver()
    
    def _init_capsolver(self):
        """初始化Capsolver配置"""
        if not HAS_CAPSOLVER:
            self.logger.warning("⚠️ Capsolver库未安装，无法解决验证码")
            return
        
        # 从环境变量获取API密钥
        api_key = os.getenv("CAPSOLVER_API_KEY")
        
        # 从配置文件获取API密钥
        if not api_key:
            try:
                import yaml
                config_path = os.path.join('input', 'config.yaml')
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)
                    capsolver_config = config.get('capsolver', {})
                    api_key = capsolver_config.get('api_key')
                    self.capsolver_timeout = capsolver_config.get('timeout', 120)
            except Exception as e:
                self.logger.debug(f"读取配置文件失败: {e}")
        
        if api_key:
            capsolver.api_key = api_key
            self.capsolver_api_key = api_key
            self.logger.info("✅ Capsolver已初始化")
        else:
            self.logger.warning("⚠️ 未找到Capsolver API密钥")
    
    async def start_local_browser(self) -> bool:
        """
        启动本地Chrome浏览器（直连模式）
        
        Returns:
            bool: 是否启动成功
        """
        try:
            self.logger.info("🚀 启动本地浏览器（直连模式）...")
            
            # 启动Playwright
            self.playwright = await async_playwright().start()
            
            # 启动Chrome浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-proxy-server',  # 禁用代理，直连
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors',
                    '--ignore-certificate-errors-spki-list',
                    '--ignore-certificate-errors-ssl-errors'
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport={'width': 1280, 'height': 720},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置超时
            self.page.set_default_timeout(self.timeout)
            
            self.logger.info("✅ 本地浏览器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 启动本地浏览器失败: {e}")
            return False
    
    async def navigate_to_inbox(self, username: str) -> bool:
        """
        导航到指定用户的yopmail收件箱
        
        Args:
            username: 邮箱用户名（不含@yopmail.com）
            
        Returns:
            bool: 是否导航成功
        """
        try:
            self.logger.info(f"📧 导航到收件箱: {username}@yopmail.com")
            
            # 访问yopmail首页
            await self.page.goto("https://yopmail.com", wait_until="domcontentloaded")
            
            # 输入邮箱用户名
            email_input = self.page.locator('input[placeholder*="Enter your inbox here"]')
            await email_input.fill(username)
            
            # 点击进入收件箱按钮
            inbox_button = self.page.locator('button[title="Check Inbox @yopmail.com"]')
            await inbox_button.click()
            
            # 等待页面加载
            await self.page.wait_for_load_state("domcontentloaded")
            
            self.logger.info("✅ 成功导航到收件箱")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 导航到收件箱失败: {e}")
            return False
    
    async def check_for_captcha(self) -> bool:
        """
        检查页面是否存在验证码
        
        Returns:
            bool: 是否存在验证码
        """
        try:
            # 检查常见的验证码指示器
            captcha_indicators = [
                'text=Complete the CAPTCHA to continue',
                '.g-recaptcha',
                'iframe[src*="recaptcha"]',
                '#r_parent',
                'text=进行人机身份验证'
            ]
            
            for indicator in captcha_indicators:
                try:
                    element = self.page.locator(indicator)
                    if await element.is_visible(timeout=2000):
                        self.logger.info(f"🔍 检测到验证码指示器: {indicator}")
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"❌ 检查验证码时出错: {e}")
            return False
    
    async def solve_captcha_with_capsolver(self) -> Optional[str]:
        """
        使用Capsolver解决reCAPTCHA验证码

        Returns:
            Optional[str]: 验证码token，失败返回None
        """
        if not HAS_CAPSOLVER or not self.capsolver_api_key:
            self.logger.error("❌ Capsolver未正确配置")
            return None

        try:
            self.logger.info("🔄 开始使用Capsolver解决reCAPTCHA...")

            # 动态获取当前页面URL
            current_url = self.page.url
            self.logger.info(f"🌐 当前页面URL: {current_url}")

            # 动态提取site key
            site_key = await self.extract_recaptcha_site_key()
            if not site_key:
                self.logger.error("❌ 无法提取reCAPTCHA site key")
                return None

            self.logger.info(f"🔑 动态提取的Site Key: {site_key}")

            start_time = time.time()

            # 构建Capsolver任务
            task_data = {
                'type': 'ReCaptchaV2TaskProxyLess',
                'websiteKey': site_key,
                'websiteURL': current_url
            }

            self.logger.info("📤 提交验证码任务到Capsolver...")

            # 解决验证码
            solution = capsolver.solve(task_data)

            solve_time = time.time() - start_time
            self.logger.info(f"⏱️ Capsolver解决耗时: {solve_time:.2f}秒")

            # 提取gRecaptchaResponse
            if solution and 'gRecaptchaResponse' in solution:
                token = solution['gRecaptchaResponse']
                self.logger.info(f"✅ 验证码解决成功，token长度: {len(token)}")
                return token
            else:
                self.logger.error("❌ Capsolver响应中没有找到gRecaptchaResponse")
                self.logger.error(f"Capsolver响应: {solution}")
                return None

        except Exception as e:
            self.logger.error(f"❌ Capsolver解决验证码失败: {e}")
            return None

    async def extract_recaptcha_site_key(self) -> Optional[str]:
        """
        从页面中动态提取reCAPTCHA的site key

        Returns:
            Optional[str]: site key，失败返回None
        """
        try:
            self.logger.info("🔍 动态提取reCAPTCHA site key...")

            # 方法1: 从页面HTML中提取
            page_content = await self.page.content()

            # Site Key提取模式
            patterns = [
                r'data-sitekey=["\']([^"\']+)["\']',
                r'"sitekey"\s*:\s*"([^"]+)"',
                r"'sitekey'\s*:\s*'([^']+)'",
                r'sitekey["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'6L[a-zA-Z0-9_-]{38}',  # reCAPTCHA格式匹配
            ]

            for pattern in patterns:
                matches = re.findall(pattern, page_content, re.IGNORECASE)
                if matches:
                    site_key = matches[0]
                    self.logger.info(f"🔑 通过模式 '{pattern}' 提取到site key: {site_key}")
                    return site_key

            # 方法2: 从reCAPTCHA iframe中提取
            try:
                recaptcha_frame = self.page.frame_locator('iframe[name*="recaptcha"]')
                frame_content = await recaptcha_frame.locator('body').inner_html()

                for pattern in patterns:
                    matches = re.findall(pattern, frame_content, re.IGNORECASE)
                    if matches:
                        site_key = matches[0]
                        self.logger.info(f"🔑 从iframe中提取到site key: {site_key}")
                        return site_key
            except:
                pass

            # 方法3: 使用JavaScript获取
            try:
                site_key = await self.page.evaluate("""
                    () => {
                        // 查找所有可能包含site key的元素
                        const elements = document.querySelectorAll('[data-sitekey]');
                        if (elements.length > 0) {
                            return elements[0].getAttribute('data-sitekey');
                        }

                        // 查找script标签中的site key
                        const scripts = document.querySelectorAll('script');
                        for (let script of scripts) {
                            const content = script.textContent || script.innerText;
                            const match = content.match(/sitekey['"]?\\s*[:=]\\s*['"]([^'"]+)['"]/i);
                            if (match) {
                                return match[1];
                            }
                        }

                        return null;
                    }
                """)

                if site_key:
                    self.logger.info(f"🔑 通过JavaScript提取到site key: {site_key}")
                    return site_key
            except:
                pass

            # 如果都失败了，使用已知的固定值作为后备
            fallback_site_key = "6LcG5v8SAAAAAOdAn2iqMEQTdVyX8t0w9T3cpdN2"
            self.logger.warning(f"⚠️ 无法动态提取site key，使用已知的固定值: {fallback_site_key}")
            return fallback_site_key

        except Exception as e:
            self.logger.error(f"❌ 提取site key失败: {e}")
            return None
    
    async def submit_captcha_token(self, token: str) -> bool:
        """
        提交验证码token到页面
        
        Args:
            token: Capsolver返回的验证码token
            
        Returns:
            bool: 是否提交成功
        """
        try:
            self.logger.info("📤 提交验证码token...")
            
            # 方法1: 直接设置g-recaptcha-response的值
            await self.page.evaluate(f"""
                // 设置reCAPTCHA响应
                const responseElement = document.querySelector('#g-recaptcha-response');
                if (responseElement) {{
                    responseElement.value = '{token}';
                    responseElement.style.display = 'block';
                }}
                
                // 触发reCAPTCHA回调
                if (window.RcCallback) {{
                    window.RcCallback();
                }} else if (window.grecaptcha && window.grecaptcha.getResponse) {{
                    // 模拟reCAPTCHA完成
                    const event = new Event('change');
                    if (responseElement) responseElement.dispatchEvent(event);
                }}
            """)
            
            # 等待页面响应
            await asyncio.sleep(2)
            
            # 检查是否还有验证码
            if not await self.check_for_captcha():
                self.logger.info("✅ 验证码token提交成功")
                return True
            else:
                self.logger.warning("⚠️ 提交后仍然存在验证码")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 提交验证码token失败: {e}")
            return False
    
    async def handle_captcha_if_present(self) -> bool:
        """
        检测并处理验证码（如果存在）

        Returns:
            bool: 是否成功处理验证码
        """
        if not await self.check_for_captcha():
            return True  # 没有验证码，直接返回成功

        self.logger.info("🔍 检测到验证码，开始处理...")

        # 处理reCAPTCHA验证码
        return await self.handle_recaptcha()

    async def handle_recaptcha(self) -> bool:
        """
        处理reCAPTCHA验证码的完整流程

        Returns:
            bool: 是否成功处理验证码
        """
        try:
            self.logger.info("🤖 开始处理reCAPTCHA验证码...")

            # 1. 点击"我不是机器人"复选框
            if not await self.click_recaptcha_checkbox():
                self.logger.error("❌ 点击reCAPTCHA复选框失败")
                return False

            # 2. 等待结果
            self.logger.info("⏳ 等待reCAPTCHA响应...")
            await asyncio.sleep(3)

            # 3. 检查是否直接通过
            if await self.is_recaptcha_solved():
                self.logger.info("✅ reCAPTCHA复选框验证直接通过")
                return True

            # 4. 检查是否需要图片验证
            elif await self.has_image_challenge():
                self.logger.info("🖼️ 需要进行图片验证，调用Capsolver...")
                return await self.solve_image_captcha_with_capsolver()

            else:
                self.logger.warning("⚠️ reCAPTCHA状态未知，尝试继续...")
                return False

        except Exception as e:
            self.logger.error(f"❌ 处理reCAPTCHA时出错: {e}")
            return False

    async def click_recaptcha_checkbox(self) -> bool:
        """
        点击reCAPTCHA的"我不是机器人"复选框

        Returns:
            bool: 是否点击成功
        """
        try:
            # 查找reCAPTCHA iframe中的复选框
            recaptcha_frame = self.page.frame_locator('iframe[name*="recaptcha"]')
            checkbox = recaptcha_frame.locator('div.recaptcha-checkbox-border')

            if await checkbox.is_visible(timeout=5000):
                await checkbox.click()
                self.logger.info("✅ 成功点击reCAPTCHA复选框")
                return True
            else:
                self.logger.error("❌ 未找到reCAPTCHA复选框")
                return False

        except Exception as e:
            self.logger.error(f"❌ 点击reCAPTCHA复选框失败: {e}")
            return False

    async def is_recaptcha_solved(self) -> bool:
        """
        检查reCAPTCHA是否已经解决（绿色勾选）

        Returns:
            bool: 是否已解决
        """
        try:
            # 检查复选框是否已勾选（绿色勾选状态）
            recaptcha_frame = self.page.frame_locator('iframe[name*="recaptcha"]')
            checked_checkbox = recaptcha_frame.locator('.recaptcha-checkbox-checked')

            is_solved = await checked_checkbox.is_visible(timeout=3000)
            if is_solved:
                self.logger.info("✅ reCAPTCHA已解决（绿色勾选）")

            return is_solved

        except Exception as e:
            self.logger.debug(f"检查reCAPTCHA状态时出错: {e}")
            return False

    async def has_image_challenge(self) -> bool:
        """
        检查是否出现了图片验证挑战

        Returns:
            bool: 是否有图片验证
        """
        try:
            # 检查是否有图片选择界面
            image_challenge_indicators = [
                'text=请选择包含',
                'text=Select all images',
                '.rc-imageselect',
                'table[role="presentation"]'
            ]

            for indicator in image_challenge_indicators:
                try:
                    element = self.page.locator(indicator)
                    if await element.is_visible(timeout=2000):
                        self.logger.info(f"🖼️ 检测到图片验证挑战: {indicator}")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.debug(f"检查图片验证时出错: {e}")
            return False

    async def solve_image_captcha_with_capsolver(self) -> bool:
        """
        使用Capsolver解决图片验证

        Returns:
            bool: 是否解决成功
        """
        try:
            # 使用Capsolver解决验证码
            token = await self.solve_captcha_with_capsolver()

            if not token:
                self.logger.error("❌ Capsolver解决图片验证失败")
                return False

            # 提交验证码token
            success = await self.submit_captcha_token(token)

            if success:
                self.logger.info("✅ 图片验证处理成功")
                # 等待页面响应
                await asyncio.sleep(3)
                return True
            else:
                self.logger.error("❌ 提交验证码token失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 解决图片验证时出错: {e}")
            return False

    async def get_resend_verification_link(self, username: str, max_attempts: int = 5, wait_interval: int = 5) -> Optional[str]:
        """
        获取指定用户的Resend验证链接

        Args:
            username: 邮箱用户名（不含@yopmail.com）
            max_attempts: 最大尝试次数
            wait_interval: 每次等待时间（秒）

        Returns:
            Optional[str]: Resend验证链接，失败返回None
        """
        self.logger.info(f"📧 开始获取验证邮件: {username}@yopmail.com")

        for attempt in range(max_attempts):
            try:
                self.logger.info(f"🔄 尝试获取邮件 (第 {attempt + 1}/{max_attempts} 次)")

                # 等待逻辑：第一次等3秒，后续等待指定时间
                if attempt == 0:
                    wait_time = 3
                    self.logger.info(f"⏳ 等待 {wait_time} 秒后开始第一次获取")
                    await asyncio.sleep(wait_time)
                elif wait_interval > 0:
                    self.logger.info(f"⏳ 等待 {wait_interval} 秒后重试获取")
                    await asyncio.sleep(wait_interval)

                # 导航到收件箱
                if not await self.navigate_to_inbox(username):
                    self.logger.error("❌ 导航到收件箱失败")
                    continue

                # 处理验证码（如果存在）
                if not await self.handle_captcha_if_present():
                    self.logger.error("❌ 验证码处理失败")
                    continue

                # 获取邮件列表
                mails = await self.get_mail_list()

                if not mails:
                    self.logger.warning("⚠️ 未找到任何邮件，继续重试...")
                    continue

                # 查找Resend验证链接
                verification_link = await self.extract_resend_verification_link(mails)

                if verification_link:
                    self.logger.info(f"✅ 成功找到验证链接: {verification_link[:50]}...")
                    return verification_link
                else:
                    self.logger.warning("⚠️ 未找到Resend验证链接，继续重试...")
                    continue

            except Exception as e:
                self.logger.error(f"❌ 获取邮件时出错 (尝试 {attempt + 1}/{max_attempts}): {e}")
                continue

        self.logger.error(f"❌ 尝试了 {max_attempts} 次都未找到验证邮件")
        return None

    async def get_mail_list(self) -> List[Dict[str, Any]]:
        """
        获取邮件列表

        Returns:
            List[Dict]: 邮件列表
        """
        try:
            self.logger.info("📋 正在获取邮件列表...")

            # 等待邮件列表加载
            await self.page.wait_for_load_state("domcontentloaded")

            # 先检查是否有验证码需要处理
            if await self.check_for_captcha():
                self.logger.info("🔍 在获取邮件列表时检测到验证码，开始处理...")
                captcha_result = await self.handle_captcha_if_present()
                if not captcha_result:
                    self.logger.warning("⚠️ 验证码处理失败，尝试继续获取邮件...")
                else:
                    self.logger.info("✅ 验证码处理成功，继续获取邮件...")
                    # 验证码处理后等待页面刷新
                    await asyncio.sleep(2)

            # 查找邮件列表iframe
            mail_iframe = self.page.locator('iframe[name="ifinbox"]')

            if not await mail_iframe.is_visible(timeout=10000):
                self.logger.error("❌ 未找到邮件列表iframe")
                # 如果还是找不到，可能是验证码没有完全解决，再次尝试
                if await self.check_for_captcha():
                    self.logger.info("🔍 仍然有验证码，再次尝试处理...")
                    await self.handle_captcha_if_present()
                    await asyncio.sleep(3)
                    # 重新查找iframe
                    if not await mail_iframe.is_visible(timeout=5000):
                        self.logger.error("❌ 验证码处理后仍未找到邮件列表iframe")
                        return []
                else:
                    return []

            # 切换到邮件iframe
            mail_frame = mail_iframe.content_frame()

            # 查找邮件项
            mail_items = await mail_frame.locator('.m').all()

            mails = []
            for i, item in enumerate(mail_items):
                try:
                    # 提取邮件信息
                    mail_info = await self.extract_mail_info(item, i)
                    if mail_info:
                        mails.append(mail_info)
                except Exception as e:
                    self.logger.debug(f"提取邮件 {i} 信息失败: {e}")
                    continue

            self.logger.info(f"📧 找到 {len(mails)} 封邮件")
            return mails

        except Exception as e:
            self.logger.error(f"❌ 获取邮件列表失败: {e}")
            return []

    async def extract_mail_info(self, mail_element, index: int) -> Optional[Dict[str, Any]]:
        """
        提取单封邮件的信息

        Args:
            mail_element: 邮件元素
            index: 邮件索引

        Returns:
            Optional[Dict]: 邮件信息
        """
        try:
            # 提取发件人
            sender_element = mail_element.locator('.lmf')
            sender = await sender_element.text_content() if await sender_element.is_visible() else "Unknown"

            # 提取主题
            subject_element = mail_element.locator('.lms')
            subject = await subject_element.text_content() if await subject_element.is_visible() else "No Subject"

            # 提取时间
            time_element = mail_element.locator('.lmh')
            time_str = await time_element.text_content() if await time_element.is_visible() else ""

            # 获取邮件ID（用于点击查看详情）
            mail_id = await mail_element.get_attribute('id') or f"mail_{index}"

            mail_info = {
                'id': mail_id,
                'sender': sender.strip(),
                'subject': subject.strip(),
                'time': time_str.strip(),
                'index': index
            }

            self.logger.debug(f"📧 邮件 {index}: {sender} - {subject}")
            return mail_info

        except Exception as e:
            self.logger.debug(f"提取邮件 {index} 信息失败: {e}")
            return None

    async def extract_resend_verification_link(self, mails: List[Dict[str, Any]]) -> Optional[str]:
        """
        从邮件列表中提取Resend验证链接

        Args:
            mails: 邮件列表

        Returns:
            Optional[str]: Resend验证链接
        """
        try:
            self.logger.info("🔍 开始查找Resend验证链接...")

            for mail in mails:
                # 检查是否是Resend邮件
                if self.is_resend_mail(mail):
                    self.logger.info(f"📧 找到Resend邮件: {mail['subject']}")

                    # 获取邮件内容
                    mail_content = await self.get_mail_content(mail)

                    if mail_content:
                        # 从邮件内容中提取验证链接
                        verification_link = self.extract_verification_link_from_content(mail_content)

                        if verification_link:
                            return verification_link

            return None

        except Exception as e:
            self.logger.error(f"❌ 提取Resend验证链接失败: {e}")
            return None

    def is_resend_mail(self, mail: Dict[str, Any]) -> bool:
        """
        判断是否是Resend验证邮件

        Args:
            mail: 邮件信息

        Returns:
            bool: 是否是Resend邮件
        """
        sender = mail.get('sender', '').lower()
        subject = mail.get('subject', '').lower()

        # Resend邮件的特征
        resend_indicators = [
            'resend',
            'verify',
            'verification',
            'confirm',
            'activate'
        ]

        # 检查发件人或主题是否包含Resend相关关键词
        for indicator in resend_indicators:
            if indicator in sender or indicator in subject:
                return True

        return False

    async def get_mail_content(self, mail: Dict[str, Any]) -> Optional[str]:
        """
        获取邮件的详细内容

        Args:
            mail: 邮件信息

        Returns:
            Optional[str]: 邮件内容
        """
        try:
            self.logger.info(f"📖 获取邮件内容: {mail['subject']}")

            # 查找邮件列表iframe
            mail_iframe = self.page.locator('iframe[name="ifinbox"]')
            mail_frame = mail_iframe.content_frame()

            # 点击邮件项
            mail_element = mail_frame.locator(f'#{mail["id"]}')
            if await mail_element.is_visible():
                await mail_element.click()
            else:
                # 如果通过ID找不到，尝试通过索引
                mail_elements = await mail_frame.locator('.m').all()
                if mail['index'] < len(mail_elements):
                    await mail_elements[mail['index']].click()
                else:
                    self.logger.error(f"❌ 无法找到邮件元素: {mail['id']}")
                    return None

            # 等待邮件内容加载
            await asyncio.sleep(2)

            # 查找邮件内容iframe
            content_iframe = self.page.locator('iframe[name="ifmail"]')

            if not await content_iframe.is_visible(timeout=10000):
                self.logger.error("❌ 未找到邮件内容iframe")
                return None

            # 切换到邮件内容iframe
            content_frame = await content_iframe.content_frame()

            # 获取邮件内容
            content = await content_frame.locator('body').text_content()

            if content:
                self.logger.info(f"✅ 成功获取邮件内容 ({len(content)} 字符)")
                return content.strip()
            else:
                self.logger.warning("⚠️ 邮件内容为空")
                return None

        except Exception as e:
            self.logger.error(f"❌ 获取邮件内容失败: {e}")
            return None

    def extract_verification_link_from_content(self, content: str) -> Optional[str]:
        """
        从邮件内容中提取验证链接

        Args:
            content: 邮件内容

        Returns:
            Optional[str]: 验证链接
        """
        try:
            self.logger.info("🔗 从邮件内容中提取验证链接...")

            # Resend验证链接的正则表达式模式
            resend_patterns = [
                r'https://[^"\s]+\.resend\.com/[^"\s]+',
                r'https://resend\.com/[^"\s]+',
                r'https://[^"\s]*resend[^"\s]*\.com/[^"\s]+',
                r'https://[^"\s]+/verify[^"\s]*',
                r'https://[^"\s]+/confirm[^"\s]*',
                r'https://[^"\s]+/activate[^"\s]*'
            ]

            for pattern in resend_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for match in matches:
                    # 清理链接（移除可能的尾随字符）
                    clean_link = self.clean_verification_link(match)
                    if self.is_valid_verification_link(clean_link):
                        self.logger.info(f"🔗 找到验证链接: {clean_link}")
                        return clean_link

            self.logger.warning("⚠️ 未在邮件内容中找到验证链接")
            return None

        except Exception as e:
            self.logger.error(f"❌ 提取验证链接失败: {e}")
            return None

    def clean_verification_link(self, link: str) -> str:
        """
        清理验证链接，移除可能的尾随字符

        Args:
            link: 原始链接

        Returns:
            str: 清理后的链接
        """
        # 移除常见的尾随字符
        trailing_chars = ['.', ',', ';', ')', ']', '}', '"', "'", '\n', '\r', '\t']

        for char in trailing_chars:
            if link.endswith(char):
                link = link.rstrip(char)

        return link.strip()

    def is_valid_verification_link(self, link: str) -> bool:
        """
        验证链接是否是有效的验证链接

        Args:
            link: 链接

        Returns:
            bool: 是否有效
        """
        if not link or len(link) < 10:
            return False

        # 检查是否是有效的URL格式
        if not link.startswith('https://'):
            return False

        # 检查是否包含验证相关的关键词
        verification_keywords = ['verify', 'confirm', 'activate', 'resend']

        for keyword in verification_keywords:
            if keyword in link.lower():
                return True

        return False

    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

            self.logger.info("✅ 浏览器已关闭")

        except Exception as e:
            self.logger.error(f"❌ 关闭浏览器失败: {e}")


# 便捷函数，用于在main.py中调用
async def get_resend_verification_link_with_playwright(
    username: str,
    max_attempts: int = 5,
    wait_interval: int = 5,
    headless: bool = False
) -> Optional[str]:
    """
    使用Playwright获取Resend验证链接的便捷函数

    Args:
        username: 邮箱用户名（不含@yopmail.com）
        max_attempts: 最大尝试次数
        wait_interval: 每次等待时间（秒）
        headless: 是否使用无头模式

    Returns:
        Optional[str]: Resend验证链接，失败返回None
    """
    client = None
    try:
        # 创建客户端
        client = PlaywrightYopmailClient(headless=headless)

        # 启动浏览器
        if not await client.start_local_browser():
            return None

        # 获取验证链接
        verification_link = await client.get_resend_verification_link(
            username, max_attempts, wait_interval
        )

        return verification_link

    except Exception as e:
        logging.getLogger(__name__).error(f"❌ 获取验证链接失败: {e}")
        return None

    finally:
        # 确保关闭浏览器
        if client:
            await client.close_browser()
