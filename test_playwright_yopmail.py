#!/usr/bin/env python3
"""
测试Playwright Yopmail模块
"""

import asyncio
import logging
import os
from scripts.playwright_yopmail import PlaywrightYopmailClient, get_resend_verification_link_with_playwright

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('playwright_yopmail_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

async def test_playwright_yopmail_basic():
    """测试基本的Playwright Yopmail功能"""
    logger = logging.getLogger(__name__)

    # 测试邮箱
    test_username = "idgreenyellow147"

    logger.info(f"🧪 开始测试Playwright Yopmail模块: {test_username}@yopmail.com")

    client = None
    try:
        # 创建客户端（服务器环境使用headless模式）
        client = PlaywrightYopmailClient(headless=True)

        # 启动浏览器
        logger.info("🚀 启动本地浏览器...")
        if not await client.start_local_browser():
            logger.error("❌ 启动浏览器失败")
            return False

        # 导航到收件箱
        logger.info("📧 导航到收件箱...")
        if not await client.navigate_to_inbox(test_username):
            logger.error("❌ 导航到收件箱失败")
            return False

        # 处理验证码
        logger.info("🔍 检查并处理验证码...")
        captcha_result = await client.handle_captcha_if_present()
        logger.info(f"🔍 验证码处理结果: {captcha_result}")

        # 即使验证码处理失败，也尝试获取邮件
        # 获取邮件列表
        logger.info("📋 获取邮件列表...")
        mails = await client.get_mail_list()

        if mails:
            logger.info(f"✅ 成功获取 {len(mails)} 封邮件")
            for i, mail in enumerate(mails):
                logger.info(f"   {i+1}. {mail['sender']} - {mail['subject']}")

            # 查找Resend验证链接
            logger.info("🔗 查找Resend验证链接...")
            verification_link = await client.extract_resend_verification_link(mails)

            if verification_link:
                logger.info(f"✅ 找到验证链接: {verification_link}")
                return True
            else:
                logger.warning("⚠️ 未找到Resend验证链接")
                return False
        else:
            logger.warning("⚠️ 未找到任何邮件")
            return False

    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

    finally:
        # 关闭浏览器
        if client:
            try:
                await client.close_browser()
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {e}")

async def test_convenience_function():
    """测试便捷函数"""
    logger = logging.getLogger(__name__)

    test_username = "idgreenyellow147"

    logger.info(f"🧪 测试便捷函数: {test_username}@yopmail.com")

    try:
        # 使用便捷函数获取验证链接（服务器环境使用headless模式）
        verification_link = await get_resend_verification_link_with_playwright(
            username=test_username,
            max_attempts=3,
            wait_interval=3,  # 减少等待时间
            headless=True  # 服务器环境使用headless模式
        )

        if verification_link:
            logger.info(f"✅ 便捷函数成功获取验证链接: {verification_link}")
            return True
        else:
            logger.warning("⚠️ 便捷函数未找到验证链接")
            return False

    except Exception as e:
        logger.error(f"❌ 便捷函数测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_network_connectivity():
    """测试网络连接"""
    logger = logging.getLogger(__name__)

    logger.info("🌐 测试网络连接...")

    try:
        import aiohttp

        async with aiohttp.ClientSession() as session:
            async with session.get('https://yopmail.com', timeout=10) as response:
                if response.status == 200:
                    logger.info("✅ 网络连接正常，可以访问yopmail.com")
                    return True
                else:
                    logger.error(f"❌ 访问yopmail.com失败，状态码: {response.status}")
                    return False

    except Exception as e:
        logger.error(f"❌ 网络连接测试失败: {e}")
        return False

async def test_captcha_handling():
    """专门测试验证码处理功能"""
    logger = logging.getLogger(__name__)

    test_username = "testcaptcha999"  # 使用一个可能触发验证码的邮箱

    logger.info(f"🧪 测试验证码处理: {test_username}@yopmail.com")

    client = None
    try:
        client = PlaywrightYopmailClient(headless=True)  # 服务器环境使用headless

        if not await client.start_local_browser():
            logger.error("❌ 启动浏览器失败")
            return False

        # 导航到收件箱
        if not await client.navigate_to_inbox(test_username):
            logger.error("❌ 导航到收件箱失败")
            return False

        # 检查验证码
        has_captcha = await client.check_for_captcha()
        logger.info(f"🔍 验证码检测结果: {has_captcha}")

        if has_captcha:
            logger.info("🤖 开始测试验证码处理...")

            # 测试完整的验证码处理流程
            captcha_result = await client.handle_captcha_if_present()
            logger.info(f"🔍 验证码处理结果: {captcha_result}")

            return captcha_result
        else:
            logger.info("ℹ️ 当前没有验证码")
            return True

    except Exception as e:
        logger.error(f"❌ 验证码测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

    finally:
        if client:
            try:
                await client.close_browser()
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {e}")

async def main():
    """主测试函数"""
    logger = logging.getLogger(__name__)

    # 测试结果统计
    test_results = {
        'network': False,
        'basic': False,
        'convenience': False,
        'captcha': False
    }

    # 检查环境
    logger.info("🔧 检查测试环境...")

    # 检查Capsolver API密钥
    capsolver_key = os.getenv("CAPSOLVER_API_KEY")
    if capsolver_key:
        logger.info("✅ Capsolver API密钥已设置")
    else:
        logger.warning("⚠️ 未设置Capsolver API密钥，图片验证功能将不可用")

    # 运行测试
    logger.info("=" * 60)
    logger.info("🚀 开始测试Playwright Yopmail模块")
    logger.info("=" * 60)

    # 测试0: 网络连接
    logger.info("\n🧪 测试0: 网络连接")
    test_results['network'] = await test_network_connectivity()

    if not test_results['network']:
        logger.error("❌ 网络连接失败，跳过后续测试")
        return

    # 测试1: 基本功能
    logger.info("\n🧪 测试1: 基本功能")
    test_results['basic'] = await test_playwright_yopmail_basic()

    # 测试2: 便捷函数
    logger.info("\n🧪 测试2: 便捷函数")
    test_results['convenience'] = await test_convenience_function()

    # 测试3: 验证码处理
    logger.info("\n🧪 测试3: 验证码处理")
    test_results['captcha'] = await test_captcha_handling()

    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info("📊 测试结果汇总")
    logger.info("=" * 60)

    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name.ljust(15)}: {status}")

    # 计算通过率
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    pass_rate = (passed_tests / total_tests) * 100

    logger.info(f"\n通过率: {passed_tests}/{total_tests} ({pass_rate:.1f}%)")

    if pass_rate >= 75:
        logger.info("🎉 测试整体通过！")
    elif pass_rate >= 50:
        logger.warning("⚠️ 测试部分通过，需要检查失败项")
    else:
        logger.error("❌ 测试整体失败，需要修复问题")

    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
