"""
Playwright注册自动化管理器
专门负责各种网站的注册流程，特别是Resend注册流程
"""

import asyncio
import random
import string
import logging
import re
from typing import Dict, Any, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON>er, BrowserContext, Page


class PlaywrightRegistration:
    """Playwright注册自动化管理器"""

    def __init__(self, browser_manager, yopmail_manager=None):
        """
        初始化Playwright注册管理器

        Args:
            browser_manager: 浏览器管理器（用于获取代理配置）
            yopmail_manager: Yopmail管理器（可选，用于邮箱验证）
        """
        self.browser_mgr = browser_manager
        self.yopmail_mgr = yopmail_manager
        self.logger = logging.getLogger(__name__)
        
        # Playwright对象
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
        # 配置
        self.headless = True  # 默认无头模式
        self.timeout = 50000  # 默认超时50秒
        
    async def connect_to_ix_browser(self, browser_info: Dict[str, Any], max_retries: int = 3, profile_id: Optional[int] = None) -> bool:
        """
        连接到IX浏览器（带重试机制）

        Args:
            browser_info: IX浏览器打开后返回的信息
            max_retries: 最大重试次数，默认3次
            profile_id: 配置文件ID（用于代理重试）

        Returns:
            是否连接成功
        """
        # 保存profile_id用于代理重试
        if profile_id:
            self._current_profile_id = profile_id
        for attempt in range(max_retries):
            try:
                self.logger.info(f"连接到IX浏览器 (尝试 {attempt + 1}/{max_retries})")

                # 启动playwright
                if not self.playwright:
                    self.playwright = await async_playwright().start()

                # 使用browser_manager提取连接信息
                if self.browser_mgr:
                    connection_info = self.browser_mgr.get_browser_connection_info(browser_info)
                    cdp_endpoint = connection_info['cdp_endpoint']
                else:
                    # 备用方案：直接从browser_info中提取
                    debug_port = None
                    ws_endpoint = None

                    if isinstance(browser_info, dict):
                        debug_port = (browser_info.get('debug_port') or
                                     browser_info.get('debugger_port') or
                                     browser_info.get('port') or
                                     browser_info.get('remote_debugging_port'))

                        ws_endpoint = (browser_info.get('ws_endpoint') or
                                      browser_info.get('websocket_debugger_url') or
                                      browser_info.get('webSocketDebuggerUrl'))

                    if ws_endpoint:
                        cdp_endpoint = ws_endpoint
                    elif debug_port:
                        cdp_endpoint = f"ws://127.0.0.1:{debug_port}"
                    else:
                        cdp_endpoint = "ws://127.0.0.1:9222"

                self.logger.debug(f"尝试连接到: {cdp_endpoint}")

                # 连接到现有的浏览器实例
                self.browser = await self.playwright.chromium.connect_over_cdp(cdp_endpoint)

                # 获取现有的上下文，或创建新的上下文
                contexts = self.browser.contexts
                if contexts:
                    # 使用第一个现有上下文
                    self.context = contexts[0]
                    self.logger.debug("使用现有浏览器上下文")
                else:
                    # 创建新的上下文（不设置任何覆盖参数，完全依赖IX指纹配置）
                    self.context = await self.browser.new_context()
                    self.logger.info("创建新的浏览器上下文（使用IX指纹配置）")

                # 获取现有页面或创建新页面
                pages = self.context.pages
                if pages:
                    # 使用第一个现有页面
                    self.page = pages[0]
                    self.logger.debug("使用现有页面")
                else:
                    # 创建新页面
                    self.page = await self.context.new_page()
                    self.logger.info("创建新页面")

                # 设置默认超时
                self.page.set_default_timeout(self.timeout)

                self.logger.info("✅ 成功连接到IX浏览器")
                return True

            except Exception as e:
                self.logger.error(f"❌ 连接IX浏览器失败 (尝试 {attempt + 1}/{max_retries}): {e}")

                # 清理失败的连接
                await self.close_browser()

                # 如果不是最后一次尝试，等待一下再重试
                if attempt < max_retries - 1:
                    self.logger.info(f"等待2秒后重试...")
                    await asyncio.sleep(2)
                else:
                    self.logger.error(f"❌ 所有重试都失败，无法连接到IX浏览器")
                    return False

        return False


    

    def generate_strong_password(self, length: int = 16) -> str:
        """
        生成符合Resend要求的强密码
        要求：至少12位，包含大小写字母、数字、特殊字符

        Args:
            length: 密码长度（最少12位）

        Returns:
            生成的强密码
        """
        if length < 12:
            length = 12

        # 确保包含所有必需字符类型
        password = [
            random.choice(string.ascii_uppercase),  # 大写字母
            random.choice(string.ascii_lowercase),  # 小写字母
            random.choice(string.digits),           # 数字
            random.choice('!@#$%^&*')              # 特殊字符
        ]

        # 填充剩余长度
        all_chars = string.ascii_letters + string.digits + '!@#$%^&*'
        for _ in range(length - 4):
            password.append(random.choice(all_chars))

        # 打乱顺序
        random.shuffle(password)
        return ''.join(password)

    def generate_realistic_email_prefix(self) -> str:
        """
        使用Faker库生成真实的邮箱前缀
        模拟真实用户的邮箱命名习惯

        Returns:
            生成的邮箱前缀
        """
        from faker import Faker
        fake = Faker('en_US')

        # 使用Faker生成的多种真实模式
        patterns = [
            # Faker内置用户名 (如: john_doe, mary_smith)
            lambda: fake.user_name(),

            # 名字+数字模式 (如: jennifer123, michael2024)
            lambda: fake.first_name().lower() + str(random.randint(1, 9999)),

            # 名字+姓氏模式 (如: johnsmith, maryjones)
            lambda: fake.first_name().lower() + fake.last_name().lower(),

            # 名字+年份模式 (如: david1990, sarah2000)
            lambda: fake.first_name().lower() + str(random.randint(1980, 2005)),

            # 姓氏+数字模式 (如: smith123, jones456)
            lambda: fake.last_name().lower() + str(random.randint(1, 999)),

            # 职业+数字模式 (如: engineer123, designer456)
            lambda: fake.job().lower().replace(' ', '') + str(random.randint(1, 999)),

            # 城市+数字模式 (如: newyork123, london456)
            lambda: fake.city().lower().replace(' ', '') + str(random.randint(1, 999)),

            # 公司+数字模式 (如: techcorp123, datasoft456)
            lambda: fake.company().lower().replace(' ', '').replace(',', '').replace('.', '') + str(random.randint(1, 999)),

            # 颜色+数字模式 (如: blue123, green456)
            lambda: fake.color_name().lower() + str(random.randint(1, 999)),

            # 随机字母数字组合 (保留原有模式)
            lambda: ''.join([random.choice(string.ascii_lowercase) + str(random.randint(0, 9)) for _ in range(random.randint(3, 4))]),
        ]

        # 随机选择一种模式生成前缀
        pattern = random.choice(patterns)
        prefix = pattern()

        # 清理特殊字符，只保留字母数字
        prefix = re.sub(r'[^a-z0-9]', '', prefix.lower())

        # 确保前缀长度合理 (6-20字符)
        if len(prefix) < 6:
            prefix += str(random.randint(10, 99))
        elif len(prefix) > 20:
            prefix = prefix[:20]

        return prefix

    def generate_yopmail_email(self, yopmail_domain: str) -> str:
        """
        生成yopmail邮箱地址

        Args:
            yopmail_domain: yopmail域名（从yopmail_manager获取）

        Returns:
            完整的yopmail邮箱地址
        """
        prefix = self.generate_realistic_email_prefix()
        return f"{prefix}@{yopmail_domain}"
    
    async def register_resend_account(self, email: str, password: str) -> Dict[str, Any]:
        """
        完整的Resend账户注册流程
        
        Args:
            email: 注册邮箱（不能是临时邮箱）
            password: 密码（至少12位，包含大小写字母、数字、特殊字符）
        
        Returns:
            dict: {"success": bool, "error": str, "message": str}
        """
        if not self.page:
            return {
                "success": False,
                "error": "BROWSER_NOT_STARTED",
                "message": "浏览器未连接，请先调用connect_to_ix_browser()"
            }
        
        signup_response = None

        # 步骤1: 监听网络响应（用于捕获服务器错误）
        async def handle_response(response):
            nonlocal signup_response
            if 'signup' in response.url:
                try:
                    # 尝试解析JSON响应
                    json_data = await response.json()
                    signup_response = json_data
                    self.logger.debug(f"捕获到signup响应: {json_data}")
                except:
                    try:
                        # 如果不是JSON，获取文本
                        text_data = await response.text()
                        signup_response = text_data
                        self.logger.debug(f"捕获到signup文本响应: {text_data[:200]}...")
                    except:
                        pass
        
        self.page.on("response", handle_response)
        
        try:
            # 步骤2: 导航到注册页面（使用带代理重试的方法）
            self.logger.info(f"开始注册Resend账户: {email}")
            page_loaded = await self.load_page_with_timeout(
                "https://resend.com/signup",
                timeout=50000,
                page_description="注册页面"
            )

            if not page_loaded:
                return {
                    "success": False,
                    "error": "PAGE_LOAD_FAILED",
                    "message": "注册页面加载失败，代理问题无法解决"
                }

            # 步骤3: 等待关键页面元素加载（使用最优单一元素判断）
            self.logger.info("等待注册页面加载完成")
            await self.page.get_by_role("heading", name="Create a Resend Account").wait_for(timeout=10000)
            self.logger.info("✅ 注册页面加载完成，可以开始填写表单")

            # 步骤4: 验证初始按钮状态（Create Account按钮应该是disabled的）
            create_button = self.page.get_by_role("button", name="Create Account")
            await create_button.wait_for(state="visible", timeout=5000)
            
            # 步骤5: 填写邮箱
            self.logger.info(f"填写邮箱: {email}")
            await self.page.get_by_role("textbox", name="Email").fill(email)

            # 步骤6: 填写密码
            self.logger.info("填写密码")
            await self.page.get_by_role("textbox", name="Password").fill(password)

            # 步骤7: 等待按钮变为可点击状态
            self.logger.info("等待按钮变为可点击")

            # 调试：检查页面上所有按钮
            all_buttons = await self.page.locator("button").all()
            self.logger.debug(f"页面上共有 {len(all_buttons)} 个按钮")

            for i, btn in enumerate(all_buttons):
                try:
                    btn_text = await btn.text_content()
                    btn_disabled = await btn.is_disabled()
                    self.logger.debug(f"按钮 {i+1}: '{btn_text}' (disabled: {btn_disabled})")
                except:
                    pass

            # 获取Create Account按钮
            create_button = self.page.get_by_role("button", name="Create Account")

            # 等待按钮可见且不是disabled
            await create_button.wait_for(state="visible", timeout=10000)

            # 检查按钮状态
            is_disabled = await create_button.is_disabled()
            self.logger.debug(f"Create Account按钮状态 - disabled: {is_disabled}")

            if is_disabled:
                self.logger.warning("按钮仍然是disabled状态，等待变为可点击...")
                # 等待按钮变为可点击
                for wait_attempt in range(10):
                    await asyncio.sleep(1)
                    is_disabled = await create_button.is_disabled()
                    if not is_disabled:
                        break
                    self.logger.info(f"等待按钮可点击... ({wait_attempt + 1}/10)")
                else:
                    raise Exception("按钮一直是disabled状态")

            # 步骤8: 随机等待后点击注册按钮
            import random
            wait_time = random.uniform(1, 3)  # 随机等待1-3秒
            self.logger.info(f"随机等待 {wait_time:.1f} 秒后点击按钮")
            await asyncio.sleep(wait_time)

            # 再次确认按钮状态
            is_disabled = await create_button.is_disabled()
            if is_disabled:
                self.logger.warning("按钮仍然是disabled状态，等待按钮亮起...")
                for wait_attempt in range(5):
                    await asyncio.sleep(1)
                    is_disabled = await create_button.is_disabled()
                    if not is_disabled:
                        self.logger.info("✅ 按钮已亮起，可以点击")
                        break
                    self.logger.info(f"等待按钮亮起... ({wait_attempt + 1}/5)")
                else:
                    raise Exception("按钮一直是disabled状态")

            self.logger.debug("点击Create Account按钮")
            button_text = await create_button.text_content()
            self.logger.debug(f"即将点击的按钮文本: '{button_text}'")

            await create_button.click()
            self.logger.info("✅ 已点击Create Account按钮")
            
            # 步骤9: 等待服务器响应
            self.logger.info("等待服务器响应")
            await self.page.wait_for_timeout(3000)
            
            # 步骤10: 检查服务器错误响应
            if signup_response:
                # 过滤HTML内容，只记录关键信息
                if isinstance(signup_response, str) and len(signup_response) > 500:
                    self.logger.debug("检查服务器响应: [HTML内容已过滤]")
                else:
                    self.logger.info(f"检查服务器响应: {signup_response}")

                if isinstance(signup_response, dict) and 'serverError' in signup_response:
                    error_msg = signup_response['serverError']
                    self.logger.warning(f"服务器返回错误: {error_msg}")
                    
                    if 'disposable email' in error_msg:
                        # 自动标记域名为拉黑
                        domain = email.split('@')[1] if '@' in email else None
                        if domain and self.yopmail_mgr:
                            self.yopmail_mgr.mark_domain_as_blocked(domain, "Resend rejected: disposable email")
                            self.logger.info(f"已将域名 {domain} 标记为拉黑")

                        return {
                            "success": False,
                            "error": "DISPOSABLE_EMAIL",
                            "message": error_msg
                        }
                    elif 'password' in error_msg.lower() or 'weak' in error_msg.lower():
                        return {
                            "success": False,
                            "error": "WEAK_PASSWORD", 
                            "message": error_msg
                        }
                    else:
                        return {
                            "success": False,
                            "error": "SERVER_ERROR",
                            "message": error_msg
                        }
                elif isinstance(signup_response, str) and 'serverError' in signup_response:
                    if 'disposable email' in signup_response:
                        # 自动标记域名为拉黑
                        domain = email.split('@')[1] if '@' in email else None
                        if domain and self.yopmail_mgr:
                            self.yopmail_mgr.mark_domain_as_blocked(domain, "Resend rejected: disposable email")
                            self.logger.info(f"已将域名 {domain} 标记为拉黑")

                        return {
                            "success": False,
                            "error": "DISPOSABLE_EMAIL",
                            "message": "Disposable email not allowed"
                        }
            
            # 步骤11: 检查注册成功页面
            self.logger.info("检查注册成功页面")
            try:
                await self.page.wait_for_selector('h2:has-text("Check your email")', timeout=30000)
                
                # 步骤12: 验证成功页面内容
                page_text = await self.page.text_content('main')
                if page_text and 'verification link' in page_text:
                    self.logger.info("✅ 注册成功")
                    return {
                        "success": True,
                        "error": None,
                        "message": "Registration successful, verification email sent"
                    }
                else:
                    return {
                        "success": False,
                        "error": "UNEXPECTED_SUCCESS_PAGE",
                        "message": "Success page format unexpected"
                    }
                    
            except:
                # 步骤13: 检查是否还在注册页面
                try:
                    await self.page.wait_for_selector('h1:has-text("Create a Resend Account")', timeout=2000)
                    return {
                        "success": False,
                        "error": "REGISTRATION_FAILED",
                        "message": "Still on registration page, unknown error"
                    }
                except:
                    return {
                        "success": False,
                        "error": "UNKNOWN_STATE",
                        "message": "Unknown page state after registration"
                    }
        
        except Exception as e:
            self.logger.error(f"注册过程中出现异常: {e}")
            return {
                "success": False,
                "error": "EXCEPTION",
                "message": f"注册过程异常: {str(e)}"
            }
        
        finally:
            # 清理：移除网络监听器
            try:
                self.page.remove_listener("response", handle_response)
            except:
                pass

    async def complete_resend_registration_with_verification(self, email: str, password: Optional[str] = None) -> Dict[str, Any]:
        """
        完整的Resend注册流程，包括邮箱验证

        Args:
            email: 注册邮箱
            password: 密码（如果不提供则自动生成）

        Returns:
            dict: 包含注册结果和验证结果
        """
        if not password:
            password = self.generate_strong_password()

        self.logger.info(f"开始完整的Resend注册流程: {email}")

        # 第一步：注册账户
        register_result = await self.register_resend_account(email, password)

        if not register_result["success"]:
            return {
                "success": False,
                "step": "REGISTRATION",
                "register_result": register_result,
                "verification_result": None,
                "password": password
            }

        # 第二步：获取验证邮件（使用新yop模块）
        verification_result = None
        try:
            # 从邮箱地址提取用户名
            email_username = email.split('@')[0] if '@' in email else email

            self.logger.info(f"等待验证邮件到达: {email_username}")
            await asyncio.sleep(5)  # 等待邮件到达

            # 使用新yop模块获取验证链接
            from scripts.yogo_python_client import get_resend_verification_link
            verification_link = get_resend_verification_link(email_username)

            if verification_link:
                self.logger.info("获取到验证链接，正在访问")

                # 使用新的verify_email方法完成验证和账户设置
                verification_result = await self.verify_email(verification_link, email)

                # 添加verification_link到结果中
                verification_result["verification_link"] = verification_link
            else:
                verification_result = {
                    "success": False,
                    "error": "NO_VERIFICATION_EMAIL",
                    "message": "未找到验证邮件"
                }

        except Exception as e:
            self.logger.error(f"邮箱验证过程出错: {e}")
            verification_result = {
                "success": False,
                "error": "VERIFICATION_EXCEPTION",
                "message": f"验证过程异常: {str(e)}"
            }

        # 提取API Key信息
        api_key = None
        if verification_result and verification_result.get("success"):
            api_key = verification_result.get("api_key")

        return {
            "success": register_result["success"] and (verification_result is None or verification_result.get("success", False)),
            "step": "COMPLETE",
            "register_result": register_result,
            "verification_result": verification_result,
            "password": password,
            "api_key": api_key,
            "email": email
        }



    async def register_with_yopmail(self, yopmail_domain: str) -> Dict[str, Any]:
        """
        使用yopmail邮箱进行注册

        Args:
            yopmail_domain: yopmail域名（从yopmail_manager获取）

        Returns:
            dict: 注册结果，包含生成的邮箱和密码
        """
        # 生成邮箱和密码
        email = self.generate_yopmail_email(yopmail_domain)
        password = self.generate_strong_password()

        self.logger.info(f"使用yopmail邮箱进行注册: {email}")

        # 执行注册
        result = await self.register_resend_account(email, password)

        # 添加生成的信息
        result["generated_email"] = email
        result["generated_password"] = password
        result["email_prefix"] = email.split('@')[0]  # 提取前缀供后续获取邮件使用

        return result

    async def verify_email(self, verification_link: str, email: str = None) -> Dict[str, Any]:
        """
        验证邮箱并完成账户设置，包括API Key创建

        Args:
            verification_link: 验证链接

        Returns:
            dict: {"success": bool, "api_key": str, "error": str, "message": str}
        """
        if not self.page:
            self.logger.error("浏览器未连接，请先调用connect_to_ix_browser()")
            return {
                "success": False,
                "api_key": None,
                "error": "BROWSER_NOT_CONNECTED",
                "message": "浏览器未连接"
            }

        try:
            self.logger.info(f"访问验证链接: {verification_link}")
            await self.page.goto(verification_link)
            await self.page.wait_for_load_state("networkidle")

            # 等待并点击 'Confirm Account' 按钮
            self.logger.info("等待 'Confirm Account' 按钮")
            confirm_button = self.page.get_by_role("button", name="Confirm Account")
            await confirm_button.wait_for(state="visible", timeout=10000)

            self.logger.info("点击 'Confirm Account' 按钮")
            await confirm_button.click()

            # 等待跳转到 onboarding 页面
            self.logger.info("等待跳转到 onboarding 页面")
            await self.page.wait_for_url("**/onboarding**", timeout=15000)

            # 验证认证成功标识 (使用最优选择器)
            success_heading = self.page.locator('h1:has-text("Account confirmed successfully")')
            await success_heading.wait_for(state="visible", timeout=10000)
            self.logger.info("✅ 账户认证成功")

            # 完成账户设置并获取API Key
            api_key_result = await self.complete_account_setup(email)

            if api_key_result["success"]:
                self.logger.info("✅ 邮箱验证和账户设置完成")
                return {
                    "success": True,
                    "api_key": api_key_result["api_key"],
                    "error": None,
                    "message": "Email verification and account setup completed"
                }
            else:
                return api_key_result

        except Exception as e:
            self.logger.error(f"❌ 验证邮箱过程失败: {e}")
            return {
                "success": False,
                "api_key": None,
                "error": "VERIFICATION_EXCEPTION",
                "message": f"验证过程异常: {str(e)}"
            }

    async def load_page_with_timeout(self, url: str, timeout: int = 50000, page_description: str = "页面") -> bool:
        """
        加载页面并检测超时，支持代理重试机制

        Args:
            url: 要加载的URL
            timeout: 超时时间（毫秒），默认50秒
            page_description: 页面描述（用于日志）

        Returns:
            是否加载成功
        """
        if not self.page:
            self.logger.error("浏览器未连接")
            return False

        max_proxy_retries = 3  # 最多重试3次代理

        for proxy_attempt in range(max_proxy_retries):
            try:
                self.logger.info(f"加载{page_description}: {url} (代理尝试 {proxy_attempt + 1}/{max_proxy_retries})")

                # 尝试加载页面，设置30秒超时
                await self.page.goto(url, timeout=timeout)

                # 如果是Resend注册页面，等待DOM加载和页面标题出现
                if "resend.com" in url and "signup" in url:
                    self.logger.info("等待DOM加载完成...")
                    await self.page.wait_for_load_state("domcontentloaded", timeout=timeout)
                    self.logger.info("等待注册页面标题出现...")
                    await self.page.get_by_role("heading", name="Create a Resend Account").wait_for(timeout=15000)
                    self.logger.info("✅ 注册页面已完全加载")
                else:
                    # 其他页面等待基本加载完成
                    await self.page.wait_for_load_state("domcontentloaded", timeout=timeout)

                self.logger.info(f"✅ {page_description}加载成功")
                return True

            except Exception as e:
                self.logger.warning(f"⚠️ {page_description}加载失败 (尝试 {proxy_attempt + 1}/{max_proxy_retries}): {e}")

                # 如果不是最后一次尝试，更新代理重试
                if proxy_attempt < max_proxy_retries - 1:
                    self.logger.info("🔄 更新代理并重试...")

                    # 步骤1: 关闭Playwright连接
                    self.logger.info("📋 步骤1: 关闭Playwright连接")
                    await self.close_browser()

                    # 步骤2: 关闭IX浏览器窗口（必须！）
                    if self.browser_mgr and hasattr(self, '_current_profile_id'):
                        self.logger.info("📋 步骤2: 关闭IX浏览器窗口")
                        close_result = self.browser_mgr.close_browser(self._current_profile_id)
                        if close_result:
                            self.logger.info("✅ IX浏览器窗口已关闭")
                        else:
                            self.logger.warning("⚠️ IX浏览器窗口关闭失败")

                        # 等待窗口完全关闭
                        await asyncio.sleep(2)

                        # 步骤3: 更新代理配置
                        self.logger.info("📋 步骤3: 更新代理配置")
                        proxy_updated = self.browser_mgr.update_proxy_for_profile(self._current_profile_id)
                        if proxy_updated:
                            self.logger.info("✅ 代理配置更新成功")
                        else:
                            self.logger.error("❌ 代理更新失败")
                            continue

                        # 步骤4: 重新打开IX浏览器窗口
                        self.logger.info("📋 步骤4: 重新打开IX浏览器窗口")
                        browser_info = self.browser_mgr.open_browser_with_retry(
                            self._current_profile_id,
                            max_retries=3,
                            retry_delay=2
                        )
                        if browser_info:
                            self.logger.info("✅ IX浏览器窗口重新打开成功")
                            
                            # 步骤5: 重新连接Playwright
                            self.logger.info("📋 步骤5: 重新连接Playwright")
                            connection_success = await self.connect_to_ix_browser(browser_info, profile_id=self._current_profile_id)
                            if connection_success:
                                self.logger.info("✅ Playwright重新连接成功，代理切换完成")
                            else:
                                self.logger.error("❌ Playwright重新连接失败")
                                continue
                        else:
                            self.logger.error("❌ IX浏览器窗口重新打开失败")
                            continue
                    else:
                        self.logger.error("❌ 缺少browser_mgr或profile_id，无法执行代理切换")
                        continue

                    # 等待一下再重试页面加载
                    await asyncio.sleep(2)
                else:
                    self.logger.error(f"❌ {page_description}加载最终失败，已尝试 {max_proxy_retries} 次代理")

        return False

    async def complete_account_setup(self, email: str = None) -> Dict[str, Any]:
        """
        完成账户设置并获取API Key
        新流程：导航到API Keys页面 -> 创建API Key -> 复制API Key

        Returns:
            dict: {"success": bool, "api_key": str, "error": str, "message": str}
        """
        try:
            self.logger.info("开始完成账户设置")

            # 确保页面已初始化
            assert self.page is not None, "Page is not initialized"

            # 步骤1: 点击左侧导航栏的"API Keys"链接
            self.logger.info("点击左侧导航栏的 'API Keys' 链接")
            api_keys_nav_link = self.page.get_by_role('link', name='API Keys')
            await api_keys_nav_link.wait_for(state="visible", timeout=10000)
            await api_keys_nav_link.click()

            # 等待页面跳转到API Keys页面，并等待"Create API Key"按钮加载完成（考虑代理延迟）
            self.logger.info("等待跳转到API Keys页面并等待页面完全加载")
            await self.page.wait_for_url("**/api-keys**", timeout=15000)

            # 步骤2: 等待"Create API Key"按钮加载完成（这是页面加载完成的标志）
            self.logger.info("等待 'Create API Key' 按钮加载完成")
            create_api_button = self.page.get_by_role('button', name='Create API Key')
            await create_api_button.wait_for(state="visible", timeout=20000)  # 增加超时时间，考虑代理延迟

            # 步骤3: 点击"Create API Key"按钮
            self.logger.info("点击 'Create API Key' 按钮")
            await create_api_button.click()

            # 步骤4: 等待创建API Key对话框出现（使用最优选择器）
            self.logger.info("等待创建API Key对话框出现")
            await asyncio.sleep(2)  # 等待对话框动画完成

            # 最优方式：等待Name文本框出现（确保对话框完全加载）
            self.logger.info("等待Name文本框出现（对话框加载完成的标志）")
            name_textbox = self.page.get_by_role('textbox', name='Name')
            await name_textbox.wait_for(state="visible", timeout=20000)  # 增加超时时间
            self.logger.info("✅ 找到Name文本框，对话框已完全加载")

            # 步骤5: 填写Name字段（使用邮箱前缀）
            # 优先使用传入的邮箱参数，否则从页面获取
            if email and "@" in email:
                # 从传入的邮箱参数中提取前缀
                api_key_name = email.split("@")[0].strip()
                self.logger.info(f"使用传入邮箱的前缀: {api_key_name}")
            else:
                # 从页面中获取当前用户邮箱信息
                try:
                    # 尝试从用户按钮中获取邮箱信息
                    user_button = self.page.locator('button:has-text("@")')
                    user_text = await user_button.text_content()
                    if user_text and "@" in user_text:
                        # 提取邮箱前缀
                        email_prefix = user_text.split("@")[0].strip()
                        api_key_name = email_prefix
                        self.logger.info(f"从用户信息中提取邮箱前缀: {api_key_name}")
                    else:
                        api_key_name = "AutoGenerated"
                        self.logger.warning("无法从页面获取邮箱信息，使用默认名称")
                except Exception as e:
                    api_key_name = "AutoGenerated"
                    self.logger.warning(f"获取邮箱前缀失败: {e}，使用默认名称")

            self.logger.info(f"填写API Key名称: {api_key_name}")
            # 使用已经找到的name_textbox，无需重新等待
            await name_textbox.fill(api_key_name)

            # 步骤6: 点击"Add"按钮创建API Key
            self.logger.info("点击 'Add' 按钮创建API Key")
            add_button = self.page.get_by_role('button', name='Add Ctrl ↩')
            await add_button.wait_for(state="visible", timeout=10000)
            await add_button.click()

            # 步骤7: 等待"View API Key"对话框出现（使用最优选择器）
            self.logger.info("等待 'View API Key' 对话框出现")
            await asyncio.sleep(2)  # 等待对话框切换

            # 最优方式：等待API Key文本框出现（确保API密钥已生成）
            self.logger.info("等待API Key文本框出现（API密钥生成完成的标志）")
            api_key_textbox = self.page.get_by_role('textbox', name='API Key')
            await api_key_textbox.wait_for(state="visible", timeout=20000)
            self.logger.info("✅ 找到API Key文本框，API密钥已生成")

            # 步骤8: 获取API Key值
            self.logger.info("获取API Key值")

            api_key = await api_key_textbox.input_value()

            # 验证API Key格式 (应该以 "re_" 开头)
            if not api_key or not api_key.startswith("re_"):
                raise Exception(f"API Key格式不正确: {api_key}")

            self.logger.info(f"✅ 成功获取API Key: {api_key}")

            # 缓存API Key到内存中
            self._cached_api_key = api_key

            # 步骤9: 点击复制按钮（第二个按钮）
            self.logger.info("复制API Key到剪贴板")

            # 在API Key文本框的容器中找到复制按钮（第二个按钮）
            api_key_container = api_key_textbox.locator('..')
            copy_buttons = api_key_container.locator('button')
            copy_button = copy_buttons.nth(1)  # 第二个按钮是复制按钮

            # 确保复制按钮可见且可点击
            await copy_button.wait_for(state="visible", timeout=5000)
            await copy_button.click()
            self.logger.info("✅ 已点击复制按钮")

            # 验证复制成功 (检查按钮是否变为active状态)
            await asyncio.sleep(1)  # 等待复制完成
            try:
                # 检查复制按钮是否变为active状态
                if await copy_button.get_attribute('active') is not None:
                    self.logger.info("✅ 复制按钮状态变为active，复制成功")
                else:
                    self.logger.warning("⚠️  无法验证复制状态，但已执行复制操作")
            except Exception as verify_error:
                self.logger.warning(f"⚠️  复制状态验证失败: {verify_error}")
                # 不抛出异常，因为复制操作可能仍然成功

            # 步骤10: 点击"Done"按钮完成流程
            self.logger.info("点击 'Done' 按钮完成流程")
            done_button = self.page.get_by_role('button', name='Done')
            await done_button.wait_for(state="visible", timeout=5000)
            await done_button.click()

            self.logger.info("✅ API Key创建和复制完成")

            # API获取成功后关闭浏览器窗口
            self.logger.info("API Key获取成功，准备关闭浏览器")
            await self.close_browser()

            return {
                "success": True,
                "api_key": api_key,
                "error": None,
                "message": "Account setup completed successfully"
            }

        except Exception as e:
            self.logger.error(f"❌ 账户设置失败: {e}")
            return {
                "success": False,
                "api_key": None,
                "error": "ACCOUNT_SETUP_FAILED",
                "message": f"账户设置失败: {str(e)}"
            }

    def get_cached_api_key(self) -> Optional[str]:
        """
        获取缓存的API Key

        Returns:
            缓存的API Key，如果没有则返回None
        """
        return getattr(self, '_cached_api_key', None)

    async def close_browser(self):
        """
        关闭浏览器和Playwright，并重置状态
        """
        try:
            if self.context:
                await self.context.close()
                self.logger.debug("浏览器上下文已关闭")

            if self.browser:
                await self.browser.close()
                self.logger.debug("浏览器已关闭")

            if self.playwright:
                await self.playwright.stop()
                self.logger.debug("Playwright已停止")

        except Exception as e:
            self.logger.error(f"关闭浏览器时出错: {e}")

        finally:
            # 重置所有状态变量
            self.playwright = None
            self.browser = None
            self.context = None
            self.page = None
            self.logger.debug("浏览器状态已重置")
