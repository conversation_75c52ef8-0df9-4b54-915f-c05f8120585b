#!/usr/bin/env python3
"""
快速测试Playwright Yopmail模块 - 适用于服务器环境
"""

import asyncio
import logging
import os
import sys
from scripts.playwright_yopmail import get_resend_verification_link_with_playwright

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def quick_test():
    """快速测试函数"""
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 开始快速测试Playwright Yopmail模块")
    
    # 检查环境
    capsolver_key = os.getenv("CAPSOLVER_API_KEY")
    if capsolver_key:
        logger.info("✅ Capsolver API密钥已设置")
    else:
        logger.warning("⚠️ 未设置Capsolver API密钥")
    
    # 测试邮箱
    test_username = "idgreenyellow147"
    
    try:
        logger.info(f"📧 测试获取邮件: {test_username}@yopmail.com")
        
        # 使用便捷函数获取验证链接
        verification_link = await get_resend_verification_link_with_playwright(
            username=test_username,
            max_attempts=2,  # 减少尝试次数，快速测试
            wait_interval=3,  # 减少等待时间
            headless=False  # 显示窗口便于观察
        )
        
        if verification_link:
            logger.info(f"✅ 成功获取验证链接: {verification_link}")
            logger.info("🎉 测试通过！")
            return True
        else:
            logger.warning("⚠️ 未找到验证链接")
            logger.info("ℹ️ 这可能是因为邮箱中没有Resend邮件，或者需要处理验证码")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

async def test_with_different_usernames():
    """使用不同的用户名测试"""
    logger = logging.getLogger(__name__)
    
    # 测试多个邮箱
    test_usernames = [
        "idgreenyellow147",
        "testuser123",
        "playwright_test"
    ]
    
    logger.info("🧪 测试多个邮箱用户名...")
    
    results = {}
    
    for username in test_usernames:
        logger.info(f"\n📧 测试用户: {username}@yopmail.com")
        
        try:
            verification_link = await get_resend_verification_link_with_playwright(
                username=username,
                max_attempts=1,  # 每个用户只尝试一次
                wait_interval=2,
                headless=True
            )
            
            results[username] = verification_link is not None
            
            if verification_link:
                logger.info(f"✅ {username}: 找到验证链接")
            else:
                logger.info(f"⚠️ {username}: 未找到验证链接")
                
        except Exception as e:
            logger.error(f"❌ {username}: 测试失败 - {e}")
            results[username] = False
    
    # 输出结果
    logger.info("\n📊 测试结果汇总:")
    for username, success in results.items():
        status = "✅" if success else "❌"
        logger.info(f"  {status} {username}@yopmail.com")
    
    success_count = sum(results.values())
    total_count = len(results)
    logger.info(f"\n成功率: {success_count}/{total_count}")
    
    return success_count > 0

async def main():
    """主函数"""
    logger = logging.getLogger(__name__)
    
    logger.info("=" * 50)
    logger.info("Playwright Yopmail 快速测试")
    logger.info("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--multi":
            # 多用户测试
            success = await test_with_different_usernames()
        else:
            logger.error("未知参数，使用 --multi 进行多用户测试")
            return
    else:
        # 单用户快速测试
        success = await quick_test()
    
    logger.info("\n" + "=" * 50)
    if success:
        logger.info("🎉 测试完成，至少有一个测试成功")
    else:
        logger.info("❌ 所有测试都失败了")
    logger.info("=" * 50)

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
