# IX浏览器配置
ix_browser:
  # 创建浏览器配置
  create_profile:
    basic:
      color: "#CC9966"           # 窗口颜色
      name: "注册中"            #窗口名称
      group_id: 205752           # 分组id 默认分组为1
      tag: ["自动注册"]          # 标签名称 (多个请传数组格式,分隔)
      site_id: "22"             # 默认打开页面（21 其他平台 22	空白页）
      site_url: ""              # 指定平台地址  site_id为21时必填
    proxy:
      proxy_type: "socks5"      # 代理类型 默认：direct (direct/http/https/socks5/ssh)
      proxy_ip: "na.d4118e06c091cce3.ipmars.vip"    # 代理ip (proxy_type非direct时必填)
      proxy_port: "4900"        # 代理端口 (proxy_type非direct时必填)
      proxy_user: "W97D5q48vK-zone-mars"            # 代理用户名
      proxy_password: "46886836"                    # 代理密码
      enable_bypass: "0"        # 直连白名单 0:关闭 1:开启 默认：0
      bypass_list: ""           # 不走代理的域名，多个通过换行间隔

  # 打开浏览器配置
  open_profile:
    profile_id: 3195            # 窗口序号 (运行时指定)

# 注册设置
registration_settings:
  interval_min: 5              # 最小间隔时间（秒）
  interval_max: 12             # 最大间隔时间（秒）
  target_count: 1               # 目标注册数量

# Cloudflare API配置
cloudflare:
  api_tokens:
    "<EMAIL>": "****************************************"
    "<EMAIL>": "1RFFIGESGPHN0kfXszPwofTUDwzYzecjzt2K55n8"

# Capsolver验证码解决配置
capsolver:
  enabled: true                    # 是否启用capsolver
  api_key: "CAP-CE2A3DE7A3F83A04839B991AE859957C663379F212F991F527EBDD88C9BBE3C2"     # Capsolver API密钥
  timeout: 120                     # 验证码解决超时时间（秒）
  max_retries: 3                   # 最大重试次数

# YOPmail验证码配置
yopmail_captcha:
  site_key: "6LcG5v8SAAAAAOdAn2iqMEQTdVyX8t0w9T3cpdN2"  # reCAPTCHA site key
  callback_function: "RcCallback"  # JavaScript回调函数名
  theme: "white"                   # reCAPTCHA主题
  size: "normal"                   # reCAPTCHA大小

